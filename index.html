<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- TODO:这个位置的title要改成动态+icon的 -->
    <title>Document</title>
    <link rel="stylesheet" href="./style/global.css">
    <link rel="stylesheet" href="./style/index.css">
</head>

<body>
    <div class="container">
        <!-- TODO:按钮位置和样式需要修改 -->
        <!-- <button id="btn">切换</button> -->
         <!-- 第一章，baby -->
        <section class="first_plane">
            <div class="baby_container">
                <div class="leftTop_text">
                    <p class="text">Hi !</p>
                    <p class="text">I`am Othniel</p>
                    <p class="text">A Web Developer</p>
                    <p class="text">Welcome</p>
                    <p class="text">My self-introduction</p>
                </div>
                <div class="leftBot_text">
                    <span>01</span>
                    <p>Player No. 970807 of Earth OnLine</p>
                </div>
            </div>
        </section>
        <!-- babylon initDom -->
        <canvas id="babylonRenderCanvas" style="width:100%;height: 100%;"> </canvas>
    </div>
    <!-- GSAP -->
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.13.0/dist/gsap.min.js"></script>
    <!-- babylon -->
    <script src="https://cdn.babylonjs.com/babylon.js"></script>
    <script src="https://cdn.babylonjs.com/loaders/babylonjs.loaders.js"></script>
    <script src="https://cdn.babylonjs.com/earcut.min.js"></script>
    <!-- ENTRY -->
    <script type="module" src="./js/index.js"></script>
</body>

</html>