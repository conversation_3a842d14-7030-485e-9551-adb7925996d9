/**
 * 更变主题
 * e: event
 * isDark: 是否为暗黑模式
*/
const changeTheme = (e,isDark) => {
    console.log(`当前主题为${isDark}`);
    const transition = document.startViewTransition(() => {
        document.documentElement.classList.toggle('dark')
    })
    const x = e.clientX
    const y = e.clientY
    // 从点击点到窗口最远边缘的距离，这个距离即为圆的半径，用于确定一个圆形裁剪路径 (clip path) 的最大尺寸，以便覆盖整个视窗。
    // 勾股定理：a² + b² = c²
    const radius = Math.sqrt(Math.max(x, (window.innerWidth - x)) ** 2 + Math.max(y, (window.innerHeight - y)) ** 2)

    transition.ready.then(() => {
        // 实现过渡的过程 circle
        document.documentElement.animate(
            {
                clipPath: [
                    `circle(0 at ${x}px ${y}px)`,
                    `circle(${radius}px at ${x}px ${y}px)`,
                ]
            },
            {
                duration: 1500,
                pseudoElement: '::view-transition-new(root)',
            }
        )
    })
};

export { changeTheme };