// gsap_animatejs
import "./animate.js";

// utils
import { changeTheme } from "./utils.js";
import {
  initEngine,
  initDefaultCamera,
  initPointLight,
  initAmbientLight,
  mouseMoveCtrlCamera,
  loadGlbModel,
  createRoundText,
  createStarField,
  createLeoConstellation
} from "./babylon.js";

// theme change
// const changeThemeBtn = document.getElementById("btn");
// changeThemeBtn.addEventListener("click", (e) => {
//     changeTheme(e,true);
// });

// babylon
// 初始化3D引擎
let engine = initEngine();
// 创建一个场景
let scene = new BABYLON.Scene(engine);
// 初始背景色设置 - 深紫色宇宙背景，更有视觉张力
scene.clearColor = new BABYLON.Color4(0.05, 0.02, 0.15, 1); // 深紫色背景
// 初始化相机
let defaultCameraInstance = initDefaultCamera(scene);

// 鼠标移动控制相机的xy
window.addEventListener("mousemove", (e) => {
  mouseMoveCtrlCamera(e, defaultCameraInstance);
});

// 第一章
// 加载婴儿模型
let babyModelInstance = loadGlbModel("baby", "./glb/", "baby.glb", scene, 0.1);
// 初始化光源,并调整光源位置 - 降低亮度以突出星空
let pointLightInstance = initPointLight(
  scene,
  0.3, // 降低光照强度
  new BABYLON.Vector3(
    -1.035673975944519,
    0.06568589806556702,
    0.9425110220909119
  )
);

// 添加环境光 - 为中心模型提供柔和照明，不影响星空效果
let ambientLightInstance = initAmbientLight(
  scene,
  0.4, // 适中的环境光强度
  new BABYLON.Color3(0.7, 0.8, 0.9) // 略带蓝色的柔和光线
);
// 加载文字圆环并添加旋转动画
const textRing = createRoundText({
  texts: ["丁", "丑", "牛","年","涧","下","水","天","生","地","和"],
  radius: 5,
  scene: scene,
  y: 0,
  textOptions: { size: 1, depth: 0.1, resolution: 64 },
  animationOptions: {
    duration: 30, // 30秒完成一圈旋转
    direction: 1   // 1为顺时针，-1为逆时针
  }
});
// 加载宇宙星空背景 - 锐利的星光点缀深空
createStarField(scene, 1200, 150);

// 在右上角创建狮子座星座图
const leoConstellation = createLeoConstellation(
  scene,
  new BABYLON.Vector3(-26.090200424194336, 11.799604415893555, -35.12043762207031),
  0.6 // 适合红框区域的缩放
);

// 调试层（可选）
// scene.debugLayer.show();

engine.runRenderLoop(() => {
  return scene && scene?.render();
});
